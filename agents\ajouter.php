<?php
require_once '../config/database.php';

$message = '';
$error = '';

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $nom = trim($_POST['nom']);
    $prenom = trim($_POST['prenom']);
    $grade = trim($_POST['grade']);
    $service_travail = trim($_POST['service_travail']);
    $nombre_jours_conge = (int)$_POST['nombre_jours_conge'];
    $reliquat = (int)$_POST['reliquat'];
    
    // Validation
    if (empty($nom) || empty($prenom) || empty($grade) || empty($service_travail)) {
        $error = "Tous les champs obligatoires doivent être remplis.";
    } elseif ($nombre_jours_conge < 0 || $nombre_jours_conge > 60) {
        $error = "Le nombre de jours de congé doit être entre 0 et 60.";
    } elseif ($reliquat < 0 || $reliquat > 365) {
        $error = "Le reliquat doit être entre 0 et 365 jours.";
    } else {
        try {
            if (addAgent($nom, $prenom, $grade, $service_travail, $nombre_jours_conge, $reliquat)) {
                $message = "Agent ajouté avec succès !";
                // Réinitialiser le formulaire
                $nom = $prenom = $grade = $service_travail = '';
                $nombre_jours_conge = 30;
                $reliquat = 0;
            } else {
                $error = "Erreur lors de l'ajout de l'agent.";
            }
        } catch (Exception $e) {
            $error = "Erreur : " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Ajouter un Agent - Gestion des Congés</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Ajouter un Agent</h1>
        </header>
        
        <nav class="main-nav">
            <ul>
                <li><a href="../index.php">Accueil</a></li>
                <li><a href="liste.php">Liste des Agents</a></li>
                <li><a href="ajouter.php" class="active">Ajouter un Agent</a></li>
                <li><a href="../conges/gestion.php">Gestion des Congés</a></li>
            </ul>
        </nav>
        
        <main>
            <div class="form-container">
                <h2>Nouveau Agent</h2>
                
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <?= htmlspecialchars($message) ?>
                        <a href="liste.php" class="btn btn-sm btn-info">Voir la liste</a>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>
                
                <form method="POST" class="agent-form">
                    <div class="form-group">
                        <label for="nom">Nom *</label>
                        <input type="text" id="nom" name="nom" required 
                               value="<?= isset($nom) ? htmlspecialchars($nom) : '' ?>"
                               placeholder="Nom de famille">
                    </div>
                    
                    <div class="form-group">
                        <label for="prenom">Prénom *</label>
                        <input type="text" id="prenom" name="prenom" required 
                               value="<?= isset($prenom) ? htmlspecialchars($prenom) : '' ?>"
                               placeholder="Prénom">
                    </div>
                    
                    <div class="form-group">
                        <label for="grade">Grade *</label>
                        <select id="grade" name="grade" required>
                            <option value="">Sélectionner un grade</option>
                            <option value="Stagiaire" <?= (isset($grade) && $grade === 'Stagiaire') ? 'selected' : '' ?>>Stagiaire</option>
                            <option value="Technicien" <?= (isset($grade) && $grade === 'Technicien') ? 'selected' : '' ?>>Technicien</option>
                            <option value="Ingénieur" <?= (isset($grade) && $grade === 'Ingénieur') ? 'selected' : '' ?>>Ingénieur</option>
                            <option value="Cadre A" <?= (isset($grade) && $grade === 'Cadre A') ? 'selected' : '' ?>>Cadre A</option>
                            <option value="Cadre B" <?= (isset($grade) && $grade === 'Cadre B') ? 'selected' : '' ?>>Cadre B</option>
                            <option value="Chef de Service" <?= (isset($grade) && $grade === 'Chef de Service') ? 'selected' : '' ?>>Chef de Service</option>
                            <option value="Directeur" <?= (isset($grade) && $grade === 'Directeur') ? 'selected' : '' ?>>Directeur</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="service_travail">Service de Travail *</label>
                        <select id="service_travail" name="service_travail" required>
                            <option value="">Sélectionner un service</option>
                            <option value="Ressources Humaines" <?= (isset($service_travail) && $service_travail === 'Ressources Humaines') ? 'selected' : '' ?>>Ressources Humaines</option>
                            <option value="Informatique" <?= (isset($service_travail) && $service_travail === 'Informatique') ? 'selected' : '' ?>>Informatique</option>
                            <option value="Technique" <?= (isset($service_travail) && $service_travail === 'Technique') ? 'selected' : '' ?>>Technique</option>
                            <option value="Administration" <?= (isset($service_travail) && $service_travail === 'Administration') ? 'selected' : '' ?>>Administration</option>
                            <option value="Commercial" <?= (isset($service_travail) && $service_travail === 'Commercial') ? 'selected' : '' ?>>Commercial</option>
                            <option value="Comptabilité" <?= (isset($service_travail) && $service_travail === 'Comptabilité') ? 'selected' : '' ?>>Comptabilité</option>
                            <option value="Production" <?= (isset($service_travail) && $service_travail === 'Production') ? 'selected' : '' ?>>Production</option>
                            <option value="Maintenance" <?= (isset($service_travail) && $service_travail === 'Maintenance') ? 'selected' : '' ?>>Maintenance</option>
                        </select>
                    </div>
                    
                    <div class="form-group">
                        <label for="nombre_jours_conge">Nombre de Jours de Congé par An</label>
                        <input type="number" id="nombre_jours_conge" name="nombre_jours_conge" 
                               min="0" max="60" 
                               value="<?= isset($nombre_jours_conge) ? $nombre_jours_conge : 30 ?>">
                        <small>Généralement 30 jours pour un cadre, 25 jours pour un employé</small>
                    </div>
                    
                    <div class="form-group">
                        <label for="reliquat">Reliquat (jours restants)</label>
                        <input type="number" id="reliquat" name="reliquat" 
                               min="0" max="365" 
                               value="<?= isset($reliquat) ? $reliquat : 0 ?>">
                        <small>Nombre de jours de congé non pris des années précédentes</small>
                    </div>
                    
                    <div class="form-actions">
                        <button type="submit" class="btn btn-primary">Ajouter l'Agent</button>
                        <a href="liste.php" class="btn btn-secondary">Annuler</a>
                    </div>
                </form>
            </div>
        </main>
        
        <footer>
            <p>&copy; 2024 Système de Gestion des Congés</p>
        </footer>
    </div>
</body>
</html>
