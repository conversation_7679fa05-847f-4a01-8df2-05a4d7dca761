<?php
/**
 * Configuration de la base de données
 * Système de gestion des congés
 */

// Configuration de la base de données
define('DB_HOST', 'localhost');
define('DB_NAME', 'gestion_conges');
define('DB_USER', 'root');
define('DB_PASS', '');
define('DB_CHARSET', 'utf8mb4');

try {
    // Création de la connexion PDO
    $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
    $options = [
        PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
        PDO::ATTR_EMULATE_PREPARES   => false,
    ];
    
    $pdo = new PDO($dsn, DB_USER, DB_PASS, $options);
    
} catch (PDOException $e) {
    // En cas d'erreur de connexion
    die("Erreur de connexion à la base de données : " . $e->getMessage());
}

/**
 * Fonction pour exécuter une requête préparée
 */
function executeQuery($sql, $params = []) {
    global $pdo;
    try {
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        return $stmt;
    } catch (PDOException $e) {
        throw new Exception("Erreur lors de l'exécution de la requête : " . $e->getMessage());
    }
}

/**
 * Fonction pour récupérer tous les agents
 */
function getAllAgents() {
    global $pdo;
    $sql = "SELECT * FROM agents ORDER BY nom, prenom";
    return $pdo->query($sql)->fetchAll();
}

/**
 * Fonction pour récupérer un agent par son ID
 */
function getAgentById($id) {
    global $pdo;
    $sql = "SELECT * FROM agents WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$id]);
    return $stmt->fetch();
}

/**
 * Fonction pour ajouter un nouvel agent
 */
function addAgent($nom, $prenom, $grade, $service_travail, $nombre_jours_conge, $reliquat = 0) {
    global $pdo;
    $sql = "INSERT INTO agents (nom, prenom, grade, service_travail, nombre_jours_conge, reliquat) 
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    return $stmt->execute([$nom, $prenom, $grade, $service_travail, $nombre_jours_conge, $reliquat]);
}

/**
 * Fonction pour mettre à jour un agent
 */
function updateAgent($id, $nom, $prenom, $grade, $service_travail, $nombre_jours_conge, $reliquat) {
    global $pdo;
    $sql = "UPDATE agents SET nom = ?, prenom = ?, grade = ?, service_travail = ?, 
            nombre_jours_conge = ?, reliquat = ? WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    return $stmt->execute([$nom, $prenom, $grade, $service_travail, $nombre_jours_conge, $reliquat, $id]);
}

/**
 * Fonction pour supprimer un agent
 */
function deleteAgent($id) {
    global $pdo;
    $sql = "DELETE FROM agents WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    return $stmt->execute([$id]);
}

/**
 * Fonction pour récupérer les congés d'un agent
 */
function getCongesByAgent($agent_id) {
    global $pdo;
    $sql = "SELECT * FROM conges WHERE agent_id = ? ORDER BY date_debut DESC";
    $stmt = $pdo->prepare($sql);
    $stmt->execute([$agent_id]);
    return $stmt->fetchAll();
}

/**
 * Fonction pour ajouter une demande de congé
 */
function addConge($agent_id, $date_debut, $date_fin, $nombre_jours, $type_conge, $motif) {
    global $pdo;
    $sql = "INSERT INTO conges (agent_id, date_debut, date_fin, nombre_jours, type_conge, motif) 
            VALUES (?, ?, ?, ?, ?, ?)";
    $stmt = $pdo->prepare($sql);
    return $stmt->execute([$agent_id, $date_debut, $date_fin, $nombre_jours, $type_conge, $motif]);
}

/**
 * Fonction pour mettre à jour le statut d'un congé
 */
function updateCongeStatut($conge_id, $statut, $approuve_par = null, $commentaires = null) {
    global $pdo;
    $sql = "UPDATE conges SET statut = ?, approuve_par = ?, commentaires = ?, date_approbation = NOW() 
            WHERE id = ?";
    $stmt = $pdo->prepare($sql);
    return $stmt->execute([$statut, $approuve_par, $commentaires, $conge_id]);
}
?>
