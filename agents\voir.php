<?php
require_once '../config/database.php';

$agent_id = $_GET['id'] ?? 0;
$agent = getAgentById($agent_id);

if (!$agent) {
    header('Location: liste.php?error=Agent non trouvé');
    exit;
}

// Récupération des congés de l'agent
$conges = getCongesByAgent($agent_id);
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Détails Agent - <?= htmlspecialchars($agent['prenom'] . ' ' . $agent['nom']) ?></title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Détails de l'Agent</h1>
        </header>
        
        <nav class="main-nav">
            <ul>
                <li><a href="../index.php">Accueil</a></li>
                <li><a href="liste.php">Liste des Agents</a></li>
                <li><a href="ajouter.php">Ajouter un Agent</a></li>
                <li><a href="../conges/gestion.php">Gestion des Congés</a></li>
            </ul>
        </nav>
        
        <main>
            <div class="page-header">
                <h2><?= htmlspecialchars($agent['prenom'] . ' ' . $agent['nom']) ?></h2>
                <div>
                    <a href="modifier.php?id=<?= $agent['id'] ?>" class="btn btn-warning">Modifier</a>
                    <a href="../conges/demande.php?agent_id=<?= $agent['id'] ?>" class="btn btn-success">Nouvelle Demande de Congé</a>
                </div>
            </div>
            
            <div class="agent-details">
                <div class="info-card">
                    <h3>Informations Personnelles</h3>
                    <div class="detail-row"><strong>Nom :</strong> <?= htmlspecialchars($agent['nom']) ?></div>
                    <div class="detail-row"><strong>Prénom :</strong> <?= htmlspecialchars($agent['prenom']) ?></div>
                    <div class="detail-row"><strong>Grade :</strong> <?= htmlspecialchars($agent['grade']) ?></div>
                    <div class="detail-row"><strong>Service de Travail :</strong> <?= htmlspecialchars($agent['service_travail']) ?></div>
                    <div class="detail-row"><strong>Jours de Congé par An :</strong> <?= $agent['nombre_jours_conge'] ?> jours</div>
                    <div class="detail-row"><strong>Reliquat :</strong> 
                        <span class="reliquat <?= $agent['reliquat'] > 0 ? 'positive' : 'zero' ?>">
                            <?= $agent['reliquat'] ?> jours
                        </span>
                    </div>
                    <div class="detail-row"><strong>Date de Création :</strong> <?= date('d/m/Y', strtotime($agent['date_creation'])) ?></div>
                </div>
                
                <div class="conges-history">
                    <h3>Historique des Congés</h3>
                    
                    <?php if (empty($conges)): ?>
                        <div class="alert alert-info">
                            <p>Aucun congé enregistré pour cet agent.</p>
                            <a href="../conges/demande.php?agent_id=<?= $agent['id'] ?>" class="btn btn-primary">Créer une demande</a>
                        </div>
                    <?php else: ?>
                        <div class="conges-list">
                            <?php foreach ($conges as $conge): ?>
                                <div class="conge-card">
                                    <div class="conge-header">
                                        <div>
                                            <strong>
                                                <?= date('d/m/Y', strtotime($conge['date_debut'])) ?> 
                                                au 
                                                <?= date('d/m/Y', strtotime($conge['date_fin'])) ?>
                                            </strong>
                                            <span class="badge"><?= $conge['nombre_jours'] ?> jour(s)</span>
                                        </div>
                                        <div class="statut-badge statut-<?= $conge['statut'] ?>">
                                            <?= ucfirst(str_replace('_', ' ', $conge['statut'])) ?>
                                        </div>
                                    </div>
                                    
                                    <div class="conge-details">
                                        <div class="detail-row"><strong>Type :</strong> <?= ucfirst($conge['type_conge']) ?></div>
                                        <?php if ($conge['motif']): ?>
                                            <div class="detail-row"><strong>Motif :</strong> <?= htmlspecialchars($conge['motif']) ?></div>
                                        <?php endif; ?>
                                        <div class="detail-row"><strong>Demandé le :</strong> <?= date('d/m/Y à H:i', strtotime($conge['date_demande'])) ?></div>
                                        <?php if ($conge['date_approbation']): ?>
                                            <div class="detail-row"><strong>Approuvé le :</strong> <?= date('d/m/Y à H:i', strtotime($conge['date_approbation'])) ?></div>
                                        <?php endif; ?>
                                        <?php if ($conge['approuve_par']): ?>
                                            <div class="detail-row"><strong>Approuvé par :</strong> <?= htmlspecialchars($conge['approuve_par']) ?></div>
                                        <?php endif; ?>
                                        <?php if ($conge['commentaires']): ?>
                                            <div class="detail-row"><strong>Commentaires :</strong> <?= htmlspecialchars($conge['commentaires']) ?></div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                        
                        <div class="summary">
                            <h4>Statistiques</h4>
                            <div class="summary-stats">
                                <div class="stat">
                                    <strong>Total demandes :</strong> <?= count($conges) ?>
                                </div>
                                <div class="stat">
                                    <strong>Jours pris cette année :</strong> 
                                    <?php
                                    $jours_pris = 0;
                                    foreach ($conges as $conge) {
                                        if ($conge['statut'] === 'approuve' || $conge['statut'] === 'termine') {
                                            $jours_pris += $conge['nombre_jours'];
                                        }
                                    }
                                    echo $jours_pris;
                                    ?> jours
                                </div>
                                <div class="stat">
                                    <strong>Jours restants :</strong> 
                                    <?= ($agent['nombre_jours_conge'] + $agent['reliquat'] - $jours_pris) ?> jours
                                </div>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
            
            <div class="actions-footer">
                <a href="liste.php" class="btn btn-secondary">Retour à la liste</a>
                <a href="modifier.php?id=<?= $agent['id'] ?>" class="btn btn-warning">Modifier cet agent</a>
                <a href="../conges/demande.php?agent_id=<?= $agent['id'] ?>" class="btn btn-success">Nouvelle demande de congé</a>
            </div>
        </main>
        
        <footer>
            <p>&copy; 2024 Système de Gestion des Congés</p>
        </footer>
    </div>
</body>
</html>
