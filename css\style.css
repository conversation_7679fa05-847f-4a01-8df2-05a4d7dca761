/* Reset et styles de base */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f4f4f4;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
    background-color: white;
    min-height: 100vh;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
}

/* Header */
header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    text-align: center;
}

header h1 {
    font-size: 2rem;
    font-weight: 300;
}

/* Navigation */
.main-nav {
    background-color: #2c3e50;
    padding: 0;
}

.main-nav ul {
    list-style: none;
    display: flex;
    flex-wrap: wrap;
}

.main-nav li {
    flex: 1;
}

.main-nav a {
    display: block;
    color: white;
    text-decoration: none;
    padding: 1rem;
    text-align: center;
    transition: background-color 0.3s;
}

.main-nav a:hover,
.main-nav a.active {
    background-color: #34495e;
}

/* Main content */
main {
    padding: 2rem 0;
    min-height: 60vh;
}

/* Dashboard */
.dashboard h2 {
    margin-bottom: 2rem;
    color: #2c3e50;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    text-align: center;
    border-left: 4px solid #667eea;
}

.stat-card h3 {
    color: #666;
    font-size: 0.9rem;
    margin-bottom: 0.5rem;
}

.stat-number {
    font-size: 2rem;
    font-weight: bold;
    color: #2c3e50;
}

/* Quick actions */
.quick-actions {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.quick-actions h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.action-buttons {
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
}

/* Buttons */
.btn {
    display: inline-block;
    padding: 0.75rem 1.5rem;
    background-color: #667eea;
    color: white;
    text-decoration: none;
    border-radius: 5px;
    border: none;
    cursor: pointer;
    font-size: 0.9rem;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #5a6fd8;
}

.btn-primary { background-color: #667eea; }
.btn-secondary { background-color: #6c757d; }
.btn-success { background-color: #28a745; }
.btn-danger { background-color: #dc3545; }
.btn-warning { background-color: #ffc107; color: #212529; }
.btn-info { background-color: #17a2b8; }

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
}

/* Forms */
.form-container {
    max-width: 600px;
    margin: 0 auto;
    background: white;
    padding: 2rem;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #2c3e50;
}

input[type="text"],
input[type="number"],
input[type="date"],
select,
textarea {
    width: 100%;
    padding: 0.75rem;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 1rem;
}

input:focus,
select:focus,
textarea:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.2);
}

small {
    display: block;
    margin-top: 0.25rem;
    color: #666;
    font-size: 0.8rem;
}

.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 2rem;
}

/* Tables */
.table-container {
    overflow-x: auto;
    margin: 2rem 0;
}

.agents-table {
    width: 100%;
    border-collapse: collapse;
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.agents-table th,
.agents-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #eee;
}

.agents-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #2c3e50;
}

.agents-table tr:hover {
    background-color: #f8f9fa;
}

.reliquat.positive {
    color: #28a745;
    font-weight: bold;
}

.reliquat.zero {
    color: #6c757d;
}

.actions {
    white-space: nowrap;
}

.actions .btn {
    margin-right: 0.5rem;
    margin-bottom: 0.5rem;
}

/* Alerts */
.alert {
    padding: 1rem;
    margin: 1rem 0;
    border-radius: 4px;
    border-left: 4px solid;
}

.alert-success {
    background-color: #d4edda;
    border-color: #28a745;
    color: #155724;
}

.alert-error {
    background-color: #f8d7da;
    border-color: #dc3545;
    color: #721c24;
}

.alert-info {
    background-color: #d1ecf1;
    border-color: #17a2b8;
    color: #0c5460;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffc107;
    color: #856404;
}

.alert h3 {
    margin-top: 0;
    margin-bottom: 1rem;
}

.alert .btn {
    margin-right: 1rem;
    margin-top: 1rem;
}

/* Page header */
.page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #eee;
}

.page-header h2 {
    color: #2c3e50;
    margin: 0;
}

/* Summary */
.summary {
    background: white;
    padding: 1.5rem;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    margin-top: 2rem;
}

.summary h3 {
    margin-bottom: 1rem;
    color: #2c3e50;
}

.summary-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.stat {
    padding: 1rem;
    background-color: #f8f9fa;
    border-radius: 4px;
}

/* Congés */
.conges-filters {
    margin: 1rem 0;
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.conges-filters .btn.active {
    background-color: #2c3e50;
}

.conges-list {
    display: grid;
    gap: 1rem;
}

.conge-card {
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
    padding: 1.5rem;
    border-left: 4px solid #ddd;
}

.conge-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 1rem;
}

.agent-info h3 {
    margin: 0 0 0.5rem 0;
    color: #2c3e50;
}

.grade, .service {
    display: inline-block;
    background-color: #f8f9fa;
    padding: 0.25rem 0.5rem;
    border-radius: 3px;
    font-size: 0.8rem;
    margin-right: 0.5rem;
}

.statut-badge {
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: bold;
    text-transform: uppercase;
}

.statut-en_attente { background-color: #fff3cd; color: #856404; }
.statut-approuve { background-color: #d4edda; color: #155724; }
.statut-refuse { background-color: #f8d7da; color: #721c24; }
.statut-en_cours { background-color: #d1ecf1; color: #0c5460; }
.statut-termine { background-color: #e2e3e5; color: #383d41; }

.conge-details {
    margin-bottom: 1rem;
}

.detail-row {
    margin-bottom: 0.5rem;
}

.conge-actions {
    border-top: 1px solid #eee;
    padding-top: 1rem;
    display: flex;
    gap: 1rem;
    flex-wrap: wrap;
    align-items: center;
}

.conge-actions input[type="text"] {
    flex: 1;
    min-width: 200px;
}

.agent-info-card {
    background-color: #f8f9fa;
    padding: 1rem;
    border-radius: 4px;
    margin: 1rem 0;
}

.info-card {
    background-color: #e3f2fd;
    padding: 1rem;
    border-radius: 4px;
    border-left: 4px solid #2196f3;
}

.info-card h4 {
    margin-bottom: 0.5rem;
    color: #1976d2;
}

/* Footer */
footer {
    background-color: #2c3e50;
    color: white;
    text-align: center;
    padding: 1rem 0;
    margin-top: 2rem;
}

/* Responsive */
@media (max-width: 768px) {
    .main-nav ul {
        flex-direction: column;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .page-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }
    
    .conge-header {
        flex-direction: column;
        gap: 1rem;
    }
    
    .conge-actions {
        flex-direction: column;
    }
    
    .conge-actions input[type="text"] {
        min-width: auto;
    }
}
