# 🚀 Guide d'Installation - Gestion des Congés

## Problème : "Table 'gestion_conges.agents' doesn't exist"

Cette erreur indique que la base de données n'a pas été créée. Voici plusieurs solutions :

## ✅ Solution 1 : Installation Automatique (Recommandée)

1. **Accédez au script d'installation :**
   ```
   http://localhost/conge/install.php
   ```

2. **Cliquez sur "Installer la Base de Données"**

3. **Accédez à l'application :**
   ```
   http://localhost/conge/
   ```

## ✅ Solution 2 : Installation Manuelle via phpMyAdmin

### Étape 1 : Accéder à phpMyAdmin
- Ouvrez votre navigateur
- Allez à : `http://localhost/phpmyadmin`
- Connectez-vous (généralement : utilisateur `root`, mot de passe vide)

### Étape 2 : Importer le fichier SQL
1. Cliquez sur l'onglet **"SQL"** en haut
2. <PERSON><PERSON>z sur **"Choisir un fichier"**
3. Sélectionnez le fichier `database/schema.sql` de votre projet
4. C<PERSON>z sur **"Exécuter"**

### Étape 3 : Vérification
- Vous devriez voir la base de données `gestion_conges` créée
- Avec les tables : `agents`, `conges`, `historique_reliquat`
- Et des données d'exemple

## ✅ Solution 3 : Installation Manuelle via Ligne de Commande

```bash
# Se connecter à MySQL
mysql -u root -p

# Exécuter le fichier SQL
source /chemin/vers/votre/projet/database/schema.sql

# Ou directement :
mysql -u root -p < database/schema.sql
```

## ✅ Solution 4 : Copier-Coller le SQL

Si les autres méthodes ne fonctionnent pas :

1. **Ouvrez le fichier `database/schema.sql`**
2. **Copiez tout le contenu**
3. **Dans phpMyAdmin :**
   - Cliquez sur l'onglet "SQL"
   - Collez le contenu
   - Cliquez sur "Exécuter"

## 🔧 Vérification de l'Installation

Après l'installation, vérifiez que :

### Base de données créée :
```sql
SHOW DATABASES LIKE 'gestion_conges';
```

### Tables créées :
```sql
USE gestion_conges;
SHOW TABLES;
```

### Données d'exemple :
```sql
SELECT COUNT(*) FROM agents;
SELECT COUNT(*) FROM conges;
```

## ⚙️ Configuration

### Vérifier la configuration de connexion
Fichier : `config/database.php`

```php
define('DB_HOST', 'localhost');     // Serveur MySQL
define('DB_NAME', 'gestion_conges'); // Nom de la base
define('DB_USER', 'root');          // Utilisateur MySQL
define('DB_PASS', '');              // Mot de passe MySQL
```

### Modifier si nécessaire :
- **DB_HOST** : Si MySQL n'est pas sur localhost
- **DB_USER** : Si vous utilisez un autre utilisateur
- **DB_PASS** : Si vous avez un mot de passe MySQL

## 🐛 Résolution de Problèmes

### Erreur "Access denied"
- Vérifiez le nom d'utilisateur et mot de passe MySQL
- Assurez-vous que MySQL est démarré

### Erreur "Connection refused"
- Vérifiez que MySQL est démarré
- Dans Laragon : Démarrer le service MySQL

### Erreur "Unknown database"
- La base de données n'existe pas
- Utilisez l'installation automatique ou manuelle

### Erreur de permissions
```sql
GRANT ALL PRIVILEGES ON gestion_conges.* TO 'root'@'localhost';
FLUSH PRIVILEGES;
```

## 📋 Structure de la Base de Données

### Table `agents`
- Stocke les informations des employés
- Champs : nom, prénom, grade, service, jours de congé, reliquat

### Table `conges`
- Stocke les demandes de congés
- Statuts : en_attente, approuve, refuse, en_cours, termine

### Table `historique_reliquat`
- Historique des modifications de reliquat

## 🎯 Après l'Installation

1. **Accédez à l'application :** `http://localhost/conge/`
2. **Testez l'ajout d'un agent**
3. **Créez une demande de congé**
4. **Testez l'approbation des demandes**

## 📞 Support

Si vous rencontrez encore des problèmes :

1. **Vérifiez les logs d'erreur PHP**
2. **Vérifiez que Laragon/XAMPP est démarré**
3. **Testez la connexion MySQL avec un autre outil**

## 🔄 Réinstallation Complète

Pour recommencer à zéro :

```sql
DROP DATABASE IF EXISTS gestion_conges;
```

Puis relancez l'installation avec une des méthodes ci-dessus.
