<?php
require_once '../config/database.php';

// Récupération de tous les agents
$agents = getAllAgents();
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Liste des Agents - Gestion des Congés</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Liste des Agents</h1>
        </header>
        
        <nav class="main-nav">
            <ul>
                <li><a href="../index.php">Accueil</a></li>
                <li><a href="liste.php" class="active">Liste des Agents</a></li>
                <li><a href="ajouter.php">Ajouter un Agent</a></li>
                <li><a href="../conges/gestion.php">Gestion des Congés</a></li>
            </ul>
        </nav>
        
        <main>
            <div class="page-header">
                <h2>Tous les Agents</h2>
                <a href="ajouter.php" class="btn btn-primary">Ajouter un Agent</a>
            </div>
            
            <?php if (empty($agents)): ?>
                <div class="alert alert-info">
                    <p>Aucun agent enregistré pour le moment.</p>
                    <a href="ajouter.php" class="btn btn-primary">Ajouter le premier agent</a>
                </div>
            <?php else: ?>
                <div class="table-container">
                    <table class="agents-table">
                        <thead>
                            <tr>
                                <th>ID</th>
                                <th>Nom</th>
                                <th>Prénom</th>
                                <th>Grade</th>
                                <th>Service de Travail</th>
                                <th>Jours de Congé</th>
                                <th>Reliquat</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($agents as $agent): ?>
                                <tr>
                                    <td><?= htmlspecialchars($agent['id']) ?></td>
                                    <td><?= htmlspecialchars($agent['nom']) ?></td>
                                    <td><?= htmlspecialchars($agent['prenom']) ?></td>
                                    <td><?= htmlspecialchars($agent['grade']) ?></td>
                                    <td><?= htmlspecialchars($agent['service_travail']) ?></td>
                                    <td><?= htmlspecialchars($agent['nombre_jours_conge']) ?></td>
                                    <td class="reliquat <?= $agent['reliquat'] > 0 ? 'positive' : 'zero' ?>">
                                        <?= htmlspecialchars($agent['reliquat']) ?>
                                    </td>
                                    <td class="actions">
                                        <a href="voir.php?id=<?= $agent['id'] ?>" class="btn btn-sm btn-info">Voir</a>
                                        <a href="modifier.php?id=<?= $agent['id'] ?>" class="btn btn-sm btn-warning">Modifier</a>
                                        <a href="../conges/demande.php?agent_id=<?= $agent['id'] ?>" class="btn btn-sm btn-success">Congé</a>
                                        <a href="supprimer.php?id=<?= $agent['id'] ?>" 
                                           class="btn btn-sm btn-danger" 
                                           onclick="return confirm('Êtes-vous sûr de vouloir supprimer cet agent ?')">
                                           Supprimer
                                        </a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </div>
                
                <div class="summary">
                    <h3>Résumé</h3>
                    <div class="summary-stats">
                        <div class="stat">
                            <strong>Total d'agents :</strong> <?= count($agents) ?>
                        </div>
                        <div class="stat">
                            <strong>Total reliquat :</strong> 
                            <?= array_sum(array_column($agents, 'reliquat')) ?> jours
                        </div>
                        <div class="stat">
                            <strong>Moyenne jours de congé :</strong> 
                            <?= round(array_sum(array_column($agents, 'nombre_jours_conge')) / count($agents), 1) ?> jours
                        </div>
                    </div>
                </div>
            <?php endif; ?>
        </main>
        
        <footer>
            <p>&copy; 2024 Système de Gestion des Congés</p>
        </footer>
    </div>
</body>
</html>
