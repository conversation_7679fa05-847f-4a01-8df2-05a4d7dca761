-- Base de données pour la gestion des congés
CREATE DATABASE IF NOT EXISTS gestion_conges CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE gestion_conges;

-- Table des agents
CREATE TABLE agents (
    id INT AUTO_INCREMENT PRIMARY KEY,
    nom VARCHAR(100) NOT NULL,
    prenom VARCHAR(100) NOT NULL,
    grade VARCHAR(50) NOT NULL,
    service_travail VARCHAR(100) NOT NULL,
    nombre_jours_conge INT NOT NULL DEFAULT 30,
    reliquat INT NOT NULL DEFAULT 0,
    date_creation TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Table des congés
CREATE TABLE conges (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id INT NOT NULL,
    date_debut DATE NOT NULL,
    date_fin DATE NOT NULL,
    nombre_jours INT NOT NULL,
    type_conge ENUM('annuel', 'maladie', 'maternite', 'paternite', 'exceptionnel') DEFAULT 'annuel',
    motif TEXT,
    statut ENUM('en_attente', 'approuve', 'refuse', 'en_cours', 'termine') DEFAULT 'en_attente',
    date_demande TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    date_approbation TIMESTAMP NULL,
    approuve_par VARCHAR(100) NULL,
    commentaires TEXT,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- Table pour l'historique des modifications de reliquat
CREATE TABLE historique_reliquat (
    id INT AUTO_INCREMENT PRIMARY KEY,
    agent_id INT NOT NULL,
    ancien_reliquat INT NOT NULL,
    nouveau_reliquat INT NOT NULL,
    motif VARCHAR(255) NOT NULL,
    date_modification TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    modifie_par VARCHAR(100) NOT NULL,
    FOREIGN KEY (agent_id) REFERENCES agents(id) ON DELETE CASCADE
);

-- Insertion de données d'exemple
INSERT INTO agents (nom, prenom, grade, service_travail, nombre_jours_conge, reliquat) VALUES
('Dupont', 'Jean', 'Cadre A', 'Ressources Humaines', 30, 5),
('Martin', 'Marie', 'Technicien', 'Informatique', 25, 10),
('Bernard', 'Pierre', 'Ingénieur', 'Technique', 30, 0),
('Durand', 'Sophie', 'Secrétaire', 'Administration', 25, 8),
('Moreau', 'Luc', 'Chef de Service', 'Commercial', 35, 12);

-- Insertion de quelques congés d'exemple
INSERT INTO conges (agent_id, date_debut, date_fin, nombre_jours, type_conge, motif, statut) VALUES
(1, '2024-07-15', '2024-07-25', 10, 'annuel', 'Vacances d\'été', 'approuve'),
(2, '2024-08-01', '2024-08-05', 5, 'annuel', 'Congés personnels', 'en_attente'),
(3, '2024-06-10', '2024-06-12', 3, 'maladie', 'Grippe', 'termine'),
(4, '2024-09-01', '2024-09-15', 15, 'annuel', 'Vacances de rentrée', 'en_attente');

-- Index pour améliorer les performances
CREATE INDEX idx_agent_nom ON agents(nom, prenom);
CREATE INDEX idx_conges_dates ON conges(date_debut, date_fin);
CREATE INDEX idx_conges_statut ON conges(statut);
CREATE INDEX idx_conges_agent ON conges(agent_id);
