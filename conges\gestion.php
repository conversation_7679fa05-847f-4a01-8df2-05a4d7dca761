<?php
require_once '../config/database.php';

// Récupération de toutes les demandes de congés avec les informations des agents
$sql = "SELECT c.*, a.nom, a.prenom, a.grade, a.service_travail, a.reliquat 
        FROM conges c 
        JOIN agents a ON c.agent_id = a.id 
        ORDER BY c.date_demande DESC";
$conges = $pdo->query($sql)->fetchAll();

// Traitement des actions (approbation, refus)
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action'])) {
    $conge_id = (int)$_POST['conge_id'];
    $action = $_POST['action'];
    $commentaires = trim($_POST['commentaires'] ?? '');
    
    if ($action === 'approuver') {
        updateCongeStatut($conge_id, 'approuve', 'Administrateur', $commentaires);
        $message = "Congé approuvé avec succès.";
    } elseif ($action === 'refuser') {
        updateCongeStatut($conge_id, 'refuse', 'Administrateur', $commentaires);
        $message = "Congé refusé.";
    } elseif ($action === 'terminer') {
        updateCongeStatut($conge_id, 'termine', 'Administrateur', $commentaires);
        $message = "Congé marqué comme terminé.";
    }
    
    // Recharger la page pour voir les changements
    header('Location: gestion.php?msg=' . urlencode($message));
    exit;
}

$message = $_GET['msg'] ?? '';
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Congés - Gestion des Congés</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Gestion des Congés</h1>
        </header>
        
        <nav class="main-nav">
            <ul>
                <li><a href="../index.php">Accueil</a></li>
                <li><a href="../agents/liste.php">Liste des Agents</a></li>
                <li><a href="../agents/ajouter.php">Ajouter un Agent</a></li>
                <li><a href="gestion.php" class="active">Gestion des Congés</a></li>
            </ul>
        </nav>
        
        <main>
            <div class="page-header">
                <h2>Toutes les Demandes de Congés</h2>
                <a href="demande.php" class="btn btn-primary">Nouvelle Demande</a>
            </div>
            
            <?php if ($message): ?>
                <div class="alert alert-success">
                    <?= htmlspecialchars($message) ?>
                </div>
            <?php endif; ?>
            
            <?php if (empty($conges)): ?>
                <div class="alert alert-info">
                    <p>Aucune demande de congé enregistrée.</p>
                    <a href="demande.php" class="btn btn-primary">Créer la première demande</a>
                </div>
            <?php else: ?>
                <div class="conges-filters">
                    <button onclick="filterConges('tous')" class="btn btn-sm btn-secondary active" id="btn-tous">Tous</button>
                    <button onclick="filterConges('en_attente')" class="btn btn-sm btn-warning" id="btn-en_attente">En Attente</button>
                    <button onclick="filterConges('approuve')" class="btn btn-sm btn-success" id="btn-approuve">Approuvés</button>
                    <button onclick="filterConges('refuse')" class="btn btn-sm btn-danger" id="btn-refuse">Refusés</button>
                    <button onclick="filterConges('en_cours')" class="btn btn-sm btn-info" id="btn-en_cours">En Cours</button>
                    <button onclick="filterConges('termine')" class="btn btn-sm btn-secondary" id="btn-termine">Terminés</button>
                </div>
                
                <div class="conges-list">
                    <?php foreach ($conges as $conge): ?>
                        <div class="conge-card" data-statut="<?= $conge['statut'] ?>">
                            <div class="conge-header">
                                <div class="agent-info">
                                    <h3><?= htmlspecialchars($conge['prenom'] . ' ' . $conge['nom']) ?></h3>
                                    <span class="grade"><?= htmlspecialchars($conge['grade']) ?></span>
                                    <span class="service"><?= htmlspecialchars($conge['service_travail']) ?></span>
                                </div>
                                <div class="statut-badge statut-<?= $conge['statut'] ?>">
                                    <?= ucfirst(str_replace('_', ' ', $conge['statut'])) ?>
                                </div>
                            </div>
                            
                            <div class="conge-details">
                                <div class="detail-row">
                                    <strong>Période :</strong> 
                                    <?= date('d/m/Y', strtotime($conge['date_debut'])) ?> 
                                    au 
                                    <?= date('d/m/Y', strtotime($conge['date_fin'])) ?>
                                </div>
                                <div class="detail-row">
                                    <strong>Durée :</strong> <?= $conge['nombre_jours'] ?> jour(s)
                                </div>
                                <div class="detail-row">
                                    <strong>Type :</strong> <?= ucfirst($conge['type_conge']) ?>
                                </div>
                                <?php if ($conge['motif']): ?>
                                    <div class="detail-row">
                                        <strong>Motif :</strong> <?= htmlspecialchars($conge['motif']) ?>
                                    </div>
                                <?php endif; ?>
                                <div class="detail-row">
                                    <strong>Demandé le :</strong> <?= date('d/m/Y à H:i', strtotime($conge['date_demande'])) ?>
                                </div>
                                <?php if ($conge['commentaires']): ?>
                                    <div class="detail-row">
                                        <strong>Commentaires :</strong> <?= htmlspecialchars($conge['commentaires']) ?>
                                    </div>
                                <?php endif; ?>
                            </div>
                            
                            <?php if ($conge['statut'] === 'en_attente'): ?>
                                <div class="conge-actions">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="conge_id" value="<?= $conge['id'] ?>">
                                        <input type="hidden" name="action" value="approuver">
                                        <input type="text" name="commentaires" placeholder="Commentaire (optionnel)" style="margin-right: 10px;">
                                        <button type="submit" class="btn btn-sm btn-success">Approuver</button>
                                    </form>
                                    
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="conge_id" value="<?= $conge['id'] ?>">
                                        <input type="hidden" name="action" value="refuser">
                                        <input type="text" name="commentaires" placeholder="Raison du refus" style="margin-right: 10px;">
                                        <button type="submit" class="btn btn-sm btn-danger">Refuser</button>
                                    </form>
                                </div>
                            <?php elseif ($conge['statut'] === 'approuve'): ?>
                                <div class="conge-actions">
                                    <form method="POST" style="display: inline;">
                                        <input type="hidden" name="conge_id" value="<?= $conge['id'] ?>">
                                        <input type="hidden" name="action" value="terminer">
                                        <button type="submit" class="btn btn-sm btn-info">Marquer comme terminé</button>
                                    </form>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endforeach; ?>
                </div>
            <?php endif; ?>
        </main>
        
        <footer>
            <p>&copy; 2024 Système de Gestion des Congés</p>
        </footer>
    </div>
    
    <script>
        function filterConges(statut) {
            const cards = document.querySelectorAll('.conge-card');
            const buttons = document.querySelectorAll('.conges-filters button');
            
            // Retirer la classe active de tous les boutons
            buttons.forEach(btn => btn.classList.remove('active'));
            
            // Ajouter la classe active au bouton cliqué
            document.getElementById('btn-' + statut).classList.add('active');
            
            // Filtrer les cartes
            cards.forEach(card => {
                if (statut === 'tous' || card.dataset.statut === statut) {
                    card.style.display = 'block';
                } else {
                    card.style.display = 'none';
                }
            });
        }
    </script>
</body>
</html>
