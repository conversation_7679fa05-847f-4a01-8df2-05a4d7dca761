<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Gestion des Congés</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Système de Gestion des Congés</h1>
        </header>
        
        <nav class="main-nav">
            <ul>
                <li><a href="index.php" class="active">Accueil</a></li>
                <li><a href="agents/liste.php">Liste des Agents</a></li>
                <li><a href="agents/ajouter.php">Ajouter un Agent</a></li>
                <li><a href="conges/gestion.php">Gestion des Congés</a></li>
            </ul>
        </nav>
        
        <main>
            <div class="dashboard">
                <h2>Tableau de Bord</h2>
                
                <div class="stats-grid">
                    <div class="stat-card">
                        <h3>Total Agents</h3>
                        <div class="stat-number">
                            <?php
                            require_once 'config/database.php';
                            if (isset($pdo)) {
                                try {
                                    $stmt = $pdo->query("SELECT COUNT(*) FROM agents");
                                    echo $stmt->fetchColumn();
                                } catch(PDOException $e) {
                                    echo "0";
                                }
                            } else {
                                echo "0";
                            }
                            ?>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Congés en Cours</h3>
                        <div class="stat-number">
                            <?php
                            if (isset($pdo)) {
                                try {
                                    $stmt = $pdo->query("SELECT COUNT(*) FROM conges WHERE statut = 'en_cours'");
                                    echo $stmt->fetchColumn();
                                } catch(PDOException $e) {
                                    echo "0";
                                }
                            } else {
                                echo "0";
                            }
                            ?>
                        </div>
                    </div>
                    
                    <div class="stat-card">
                        <h3>Demandes en Attente</h3>
                        <div class="stat-number">
                            <?php
                            if (isset($pdo)) {
                                try {
                                    $stmt = $pdo->query("SELECT COUNT(*) FROM conges WHERE statut = 'en_attente'");
                                    echo $stmt->fetchColumn();
                                } catch(PDOException $e) {
                                    echo "0";
                                }
                            } else {
                                echo "0";
                            }
                            ?>
                        </div>
                    </div>
                </div>
                
                <div class="quick-actions">
                    <h3>Actions Rapides</h3>
                    <div class="action-buttons">
                        <a href="agents/ajouter.php" class="btn btn-primary">Ajouter un Agent</a>
                        <a href="conges/demande.php" class="btn btn-secondary">Nouvelle Demande de Congé</a>
                        <a href="agents/liste.php" class="btn btn-info">Voir tous les Agents</a>
                    </div>
                </div>
            </div>
        </main>
        
        <footer>
            <p>&copy; 2024 Système de Gestion des Congés</p>
        </footer>
    </div>
</body>
</html>
