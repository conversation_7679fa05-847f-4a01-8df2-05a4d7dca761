<?php
require_once '../config/database.php';

$message = '';
$error = '';
$agent_id = $_GET['agent_id'] ?? '';

// Récupération de tous les agents pour le formulaire
$agents = getAllAgents();

// Traitement du formulaire
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $agent_id = (int)$_POST['agent_id'];
    $date_debut = $_POST['date_debut'];
    $date_fin = $_POST['date_fin'];
    $type_conge = $_POST['type_conge'];
    $motif = trim($_POST['motif']);
    
    // Calcul du nombre de jours
    $debut = new DateTime($date_debut);
    $fin = new DateTime($date_fin);
    $interval = $debut->diff($fin);
    $nombre_jours = $interval->days + 1; // +1 pour inclure le jour de fin
    
    // Validation
    if (empty($agent_id) || empty($date_debut) || empty($date_fin) || empty($type_conge)) {
        $error = "Tous les champs obligatoires doivent être remplis.";
    } elseif ($debut > $fin) {
        $error = "La date de fin doit être postérieure à la date de début.";
    } elseif ($debut < new DateTime()) {
        $error = "La date de début ne peut pas être dans le passé.";
    } elseif ($nombre_jours > 60) {
        $error = "La durée du congé ne peut pas dépasser 60 jours.";
    } else {
        try {
            if (addConge($agent_id, $date_debut, $date_fin, $nombre_jours, $type_conge, $motif)) {
                $message = "Demande de congé créée avec succès !";
                // Réinitialiser le formulaire
                $agent_id = $date_debut = $date_fin = $type_conge = $motif = '';
            } else {
                $error = "Erreur lors de la création de la demande.";
            }
        } catch (Exception $e) {
            $error = "Erreur : " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demande de Congé - Gestion des Congés</title>
    <link rel="stylesheet" href="../css/style.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>Nouvelle Demande de Congé</h1>
        </header>
        
        <nav class="main-nav">
            <ul>
                <li><a href="../index.php">Accueil</a></li>
                <li><a href="../agents/liste.php">Liste des Agents</a></li>
                <li><a href="../agents/ajouter.php">Ajouter un Agent</a></li>
                <li><a href="gestion.php">Gestion des Congés</a></li>
            </ul>
        </nav>
        
        <main>
            <div class="form-container">
                <h2>Créer une Demande de Congé</h2>
                
                <?php if ($message): ?>
                    <div class="alert alert-success">
                        <?= htmlspecialchars($message) ?>
                        <a href="gestion.php" class="btn btn-sm btn-info">Voir toutes les demandes</a>
                    </div>
                <?php endif; ?>
                
                <?php if ($error): ?>
                    <div class="alert alert-error">
                        <?= htmlspecialchars($error) ?>
                    </div>
                <?php endif; ?>
                
                <?php if (empty($agents)): ?>
                    <div class="alert alert-warning">
                        <p>Aucun agent enregistré. Vous devez d'abord ajouter des agents.</p>
                        <a href="../agents/ajouter.php" class="btn btn-primary">Ajouter un agent</a>
                    </div>
                <?php else: ?>
                    <form method="POST" class="conge-form" id="congeForm">
                        <div class="form-group">
                            <label for="agent_id">Agent *</label>
                            <select id="agent_id" name="agent_id" required onchange="updateAgentInfo()">
                                <option value="">Sélectionner un agent</option>
                                <?php foreach ($agents as $agent): ?>
                                    <option value="<?= $agent['id'] ?>" 
                                            data-reliquat="<?= $agent['reliquat'] ?>"
                                            data-jours-conge="<?= $agent['nombre_jours_conge'] ?>"
                                            <?= ($agent_id == $agent['id']) ? 'selected' : '' ?>>
                                        <?= htmlspecialchars($agent['prenom'] . ' ' . $agent['nom'] . ' - ' . $agent['grade']) ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>
                        
                        <div id="agent-info" class="agent-info" style="display: none;">
                            <div class="info-card">
                                <h4>Informations de l'agent</h4>
                                <p><strong>Jours de congé annuels :</strong> <span id="jours-conge"></span></p>
                                <p><strong>Reliquat disponible :</strong> <span id="reliquat"></span> jours</p>
                            </div>
                        </div>
                        
                        <div class="form-row">
                            <div class="form-group">
                                <label for="date_debut">Date de Début *</label>
                                <input type="date" id="date_debut" name="date_debut" required 
                                       value="<?= isset($date_debut) ? $date_debut : '' ?>"
                                       onchange="calculateDays()">
                            </div>
                            
                            <div class="form-group">
                                <label for="date_fin">Date de Fin *</label>
                                <input type="date" id="date_fin" name="date_fin" required 
                                       value="<?= isset($date_fin) ? $date_fin : '' ?>"
                                       onchange="calculateDays()">
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label for="nombre_jours_calc">Nombre de Jours</label>
                            <input type="text" id="nombre_jours_calc" readonly 
                                   placeholder="Sera calculé automatiquement">
                        </div>
                        
                        <div class="form-group">
                            <label for="type_conge">Type de Congé *</label>
                            <select id="type_conge" name="type_conge" required>
                                <option value="">Sélectionner un type</option>
                                <option value="annuel" <?= (isset($type_conge) && $type_conge === 'annuel') ? 'selected' : '' ?>>Congé Annuel</option>
                                <option value="maladie" <?= (isset($type_conge) && $type_conge === 'maladie') ? 'selected' : '' ?>>Congé Maladie</option>
                                <option value="maternite" <?= (isset($type_conge) && $type_conge === 'maternite') ? 'selected' : '' ?>>Congé Maternité</option>
                                <option value="paternite" <?= (isset($type_conge) && $type_conge === 'paternite') ? 'selected' : '' ?>>Congé Paternité</option>
                                <option value="exceptionnel" <?= (isset($type_conge) && $type_conge === 'exceptionnel') ? 'selected' : '' ?>>Congé Exceptionnel</option>
                            </select>
                        </div>
                        
                        <div class="form-group">
                            <label for="motif">Motif</label>
                            <textarea id="motif" name="motif" rows="3" 
                                      placeholder="Précisez le motif de votre demande (optionnel)"><?= isset($motif) ? htmlspecialchars($motif) : '' ?></textarea>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Créer la Demande</button>
                            <a href="gestion.php" class="btn btn-secondary">Annuler</a>
                        </div>
                    </form>
                <?php endif; ?>
            </div>
        </main>
        
        <footer>
            <p>&copy; 2024 Système de Gestion des Congés</p>
        </footer>
    </div>
    
    <script>
        function updateAgentInfo() {
            const select = document.getElementById('agent_id');
            const selectedOption = select.options[select.selectedIndex];
            const infoDiv = document.getElementById('agent-info');
            
            if (selectedOption.value) {
                const reliquat = selectedOption.dataset.reliquat;
                const joursConge = selectedOption.dataset.joursConge;
                
                document.getElementById('reliquat').textContent = reliquat;
                document.getElementById('jours-conge').textContent = joursConge;
                infoDiv.style.display = 'block';
            } else {
                infoDiv.style.display = 'none';
            }
        }
        
        function calculateDays() {
            const dateDebut = document.getElementById('date_debut').value;
            const dateFin = document.getElementById('date_fin').value;
            
            if (dateDebut && dateFin) {
                const debut = new Date(dateDebut);
                const fin = new Date(dateFin);
                
                if (fin >= debut) {
                    const diffTime = Math.abs(fin - debut);
                    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24)) + 1;
                    document.getElementById('nombre_jours_calc').value = diffDays + ' jour(s)';
                } else {
                    document.getElementById('nombre_jours_calc').value = 'Dates invalides';
                }
            } else {
                document.getElementById('nombre_jours_calc').value = '';
            }
        }
        
        // Définir la date minimum à aujourd'hui
        document.getElementById('date_debut').min = new Date().toISOString().split('T')[0];
        document.getElementById('date_fin').min = new Date().toISOString().split('T')[0];
    </script>
</body>
</html>
