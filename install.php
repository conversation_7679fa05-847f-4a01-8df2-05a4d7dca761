<?php
/**
 * Script d'installation pour la base de données
 * Système de gestion des congés
 */

// Configuration de la base de données
$host = 'localhost';
$username = 'root';
$password = '';
$database = 'gestion_conges';

$message = '';
$error = '';

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    try {
        // Connexion à MySQL sans spécifier de base de données
        $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // Lire le fichier SQL
        $sqlFile = 'database/schema.sql';
        if (!file_exists($sqlFile)) {
            throw new Exception("Le fichier schema.sql n'existe pas dans le dossier database/");
        }
        
        $sql = file_get_contents($sqlFile);
        
        // Diviser le SQL en requêtes individuelles
        $queries = array_filter(array_map('trim', explode(';', $sql)));
        
        $successCount = 0;
        foreach ($queries as $query) {
            if (!empty($query)) {
                $pdo->exec($query);
                $successCount++;
            }
        }
        
        $message = "Installation réussie ! $successCount requêtes exécutées. La base de données '$database' a été créée avec succès.";
        
    } catch (PDOException $e) {
        $error = "Erreur de base de données : " . $e->getMessage();
    } catch (Exception $e) {
        $error = "Erreur : " . $e->getMessage();
    }
}

// Vérifier si la base de données existe déjà
$dbExists = false;
try {
    $pdo = new PDO("mysql:host=$host;charset=utf8mb4", $username, $password);
    $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    
    $stmt = $pdo->query("SHOW DATABASES LIKE '$database'");
    $dbExists = $stmt->rowCount() > 0;
    
    if ($dbExists) {
        // Vérifier si les tables existent
        $pdo->exec("USE $database");
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $tablesExist = in_array('agents', $tables) && in_array('conges', $tables);
    }
} catch (PDOException $e) {
    // Erreur de connexion, probablement problème de configuration
}
?>

<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Installation - Gestion des Congés</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        .install-container {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
            max-width: 600px;
            width: 100%;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 2rem;
        }
        .alert {
            padding: 1rem;
            margin: 1rem 0;
            border-radius: 5px;
            border-left: 4px solid;
        }
        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }
        .alert-error {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }
        .alert-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }
        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }
        .btn {
            display: inline-block;
            padding: 0.75rem 1.5rem;
            background-color: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 1rem;
            transition: background-color 0.3s;
        }
        .btn:hover {
            background-color: #5a6fd8;
        }
        .btn-success {
            background-color: #28a745;
        }
        .btn-success:hover {
            background-color: #218838;
        }
        .config-info {
            background-color: #f8f9fa;
            padding: 1rem;
            border-radius: 5px;
            margin: 1rem 0;
        }
        .config-info h3 {
            margin-top: 0;
            color: #2c3e50;
        }
        .status-check {
            margin: 1rem 0;
        }
        .status-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0.5rem 0;
            border-bottom: 1px solid #eee;
        }
        .status-ok {
            color: #28a745;
            font-weight: bold;
        }
        .status-error {
            color: #dc3545;
            font-weight: bold;
        }
        .text-center {
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="install-container">
        <h1>🚀 Installation - Gestion des Congés</h1>
        
        <?php if ($message): ?>
            <div class="alert alert-success">
                <?= htmlspecialchars($message) ?>
                <br><br>
                <a href="index.php" class="btn btn-success">Accéder à l'application</a>
            </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                <?= htmlspecialchars($error) ?>
            </div>
        <?php endif; ?>
        
        <div class="config-info">
            <h3>Configuration actuelle</h3>
            <div class="status-check">
                <div class="status-item">
                    <span>Serveur MySQL :</span>
                    <span class="<?= isset($pdo) ? 'status-ok' : 'status-error' ?>">
                        <?= isset($pdo) ? '✓ Connecté' : '✗ Erreur de connexion' ?>
                    </span>
                </div>
                <div class="status-item">
                    <span>Base de données '<?= $database ?>' :</span>
                    <span class="<?= $dbExists ? 'status-ok' : 'status-error' ?>">
                        <?= $dbExists ? '✓ Existe' : '✗ N\'existe pas' ?>
                    </span>
                </div>
                <?php if ($dbExists): ?>
                <div class="status-item">
                    <span>Tables :</span>
                    <span class="<?= isset($tablesExist) && $tablesExist ? 'status-ok' : 'status-error' ?>">
                        <?= isset($tablesExist) && $tablesExist ? '✓ Créées' : '✗ Manquantes' ?>
                    </span>
                </div>
                <?php endif; ?>
                <div class="status-item">
                    <span>Fichier schema.sql :</span>
                    <span class="<?= file_exists('database/schema.sql') ? 'status-ok' : 'status-error' ?>">
                        <?= file_exists('database/schema.sql') ? '✓ Trouvé' : '✗ Manquant' ?>
                    </span>
                </div>
            </div>
        </div>
        
        <?php if (!$dbExists || !isset($tablesExist) || !$tablesExist): ?>
            <div class="alert alert-info">
                <h4>Installation requise</h4>
                <p>La base de données ou les tables n'existent pas. Cliquez sur le bouton ci-dessous pour créer automatiquement la base de données avec toutes les tables et données d'exemple.</p>
            </div>
            
            <div class="text-center">
                <form method="POST">
                    <button type="submit" class="btn">
                        🔧 Installer la Base de Données
                    </button>
                </form>
            </div>
        <?php else: ?>
            <div class="alert alert-success">
                <h4>Installation complète !</h4>
                <p>La base de données et toutes les tables sont déjà créées. Vous pouvez accéder à l'application.</p>
            </div>
            
            <div class="text-center">
                <a href="index.php" class="btn btn-success">
                    🏠 Accéder à l'Application
                </a>
            </div>
        <?php endif; ?>
        
        <div class="config-info">
            <h3>Instructions manuelles (alternative)</h3>
            <p>Si l'installation automatique ne fonctionne pas, vous pouvez :</p>
            <ol>
                <li>Ouvrir phpMyAdmin ou votre client MySQL</li>
                <li>Importer le fichier <code>database/schema.sql</code></li>
                <li>Ou copier-coller le contenu du fichier dans l'onglet SQL</li>
            </ol>
        </div>
        
        <div class="config-info">
            <h3>Configuration de connexion</h3>
            <p><strong>Host :</strong> <?= $host ?></p>
            <p><strong>Utilisateur :</strong> <?= $username ?></p>
            <p><strong>Base de données :</strong> <?= $database ?></p>
            <p><small>Si ces paramètres ne correspondent pas à votre configuration, modifiez le fichier <code>config/database.php</code></small></p>
        </div>
    </div>
</body>
</html>
