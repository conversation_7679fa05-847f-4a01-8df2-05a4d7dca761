# Système de Gestion des Congés

Une application web complète pour la gestion des congés des employés, développée en PHP avec MySQL.

## Fonctionnalités

### Gestion des Agents
- ✅ Ajouter un nouvel agent avec ses informations complètes
- ✅ Lister tous les agents avec leurs détails
- ✅ Voir le profil détaillé d'un agent
- ✅ Modifier les informations d'un agent
- ✅ Supprimer un agent
- ✅ Suivi du reliquat de congés

### Gestion des Congés
- ✅ Créer une demande de congé
- ✅ Approuver ou refuser les demandes
- ✅ Suivre le statut des congés (en attente, approuvé, refusé, en cours, terminé)
- ✅ Historique complet des congés par agent
- ✅ Calcul automatique du nombre de jours
- ✅ Différents types de congés (annuel, maladie, maternité, etc.)

### Tableau de Bord
- ✅ Statistiques générales
- ✅ Vue d'ensemble des demandes en cours
- ✅ Actions rapides

## Structure de la Base de Données

### Table `agents`
- `id` : Identifiant unique
- `nom` : Nom de famille
- `prenom` : Prénom
- `grade` : Grade de l'agent
- `service_travail` : Service de travail
- `nombre_jours_conge` : Nombre de jours de congé annuels
- `reliquat` : Jours de congé restants des années précédentes

### Table `conges`
- `id` : Identifiant unique
- `agent_id` : Référence vers l'agent
- `date_debut` : Date de début du congé
- `date_fin` : Date de fin du congé
- `nombre_jours` : Nombre de jours du congé
- `type_conge` : Type de congé (annuel, maladie, etc.)
- `motif` : Motif de la demande
- `statut` : Statut de la demande
- `date_demande` : Date de la demande
- `date_approbation` : Date d'approbation
- `approuve_par` : Personne qui a approuvé
- `commentaires` : Commentaires

### Table `historique_reliquat`
- Suivi des modifications de reliquat

## Installation

### Prérequis
- Serveur web (Apache/Nginx)
- PHP 7.4 ou supérieur
- MySQL 5.7 ou supérieur
- Laragon, XAMPP, WAMP ou environnement similaire

### Étapes d'installation

1. **Cloner ou télécharger le projet**
   ```bash
   git clone [url-du-projet]
   cd conge
   ```

2. **Configuration de la base de données**
   - Ouvrir phpMyAdmin ou votre client MySQL
   - Importer le fichier `database/schema.sql`
   - Ou exécuter les commandes SQL du fichier

3. **Configuration de la connexion**
   - Modifier le fichier `config/database.php` si nécessaire
   - Ajuster les paramètres de connexion MySQL :
     ```php
     define('DB_HOST', 'localhost');
     define('DB_NAME', 'gestion_conges');
     define('DB_USER', 'root');
     define('DB_PASS', '');
     ```

4. **Démarrer le serveur**
   - Avec Laragon : Démarrer Apache et MySQL
   - Accéder à `http://localhost/conge`

## Utilisation

### Première utilisation
1. Accéder à l'application via votre navigateur
2. Ajouter des agents via "Ajouter un Agent"
3. Créer des demandes de congés
4. Gérer les approbations via "Gestion des Congés"

### Navigation
- **Accueil** : Tableau de bord avec statistiques
- **Liste des Agents** : Voir tous les agents
- **Ajouter un Agent** : Formulaire d'ajout
- **Gestion des Congés** : Gérer toutes les demandes

### Types de congés disponibles
- Congé annuel
- Congé maladie
- Congé maternité
- Congé paternité
- Congé exceptionnel

### Statuts des demandes
- **En attente** : Demande créée, en attente d'approbation
- **Approuvé** : Demande approuvée par l'administrateur
- **Refusé** : Demande refusée
- **En cours** : Congé actuellement en cours
- **Terminé** : Congé terminé

## Structure du Projet

```
conge/
├── index.php              # Page d'accueil
├── config/
│   └── database.php       # Configuration BDD et fonctions
├── agents/
│   ├── liste.php          # Liste des agents
│   ├── ajouter.php        # Ajouter un agent
│   └── voir.php           # Détails d'un agent
├── conges/
│   ├── gestion.php        # Gestion des congés
│   └── demande.php        # Nouvelle demande
├── css/
│   └── style.css          # Styles CSS
├── database/
│   └── schema.sql         # Structure de la BDD
└── README.md              # Documentation
```

## Fonctionnalités Avancées

### Calcul Automatique
- Calcul automatique du nombre de jours entre deux dates
- Validation des dates (pas de dates passées)
- Vérification de la cohérence des dates

### Interface Responsive
- Design adaptatif pour mobile et desktop
- Interface moderne et intuitive
- Filtres et recherche

### Sécurité
- Utilisation de requêtes préparées (PDO)
- Échappement des données d'affichage
- Validation côté serveur

## Personnalisation

### Ajouter de nouveaux grades
Modifier le fichier `agents/ajouter.php` dans la section des options du select `grade`.

### Ajouter de nouveaux services
Modifier le fichier `agents/ajouter.php` dans la section des options du select `service_travail`.

### Modifier les types de congés
Modifier la structure de la base de données et les formulaires correspondants.

## Support

Pour toute question ou problème :
1. Vérifier la configuration de la base de données
2. Vérifier les logs d'erreur PHP
3. S'assurer que tous les fichiers sont présents

## Licence

Ce projet est développé pour un usage interne de gestion des congés.
